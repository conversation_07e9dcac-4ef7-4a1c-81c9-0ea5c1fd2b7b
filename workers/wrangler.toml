# Wrangler configuration for Syndicaps Image Optimization Worker

account_id = "24a0c424061d2684ac21f5dd6284f906"
name = "syndicaps-image-optimizer"
main = "image-optimizer.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# Worker settings
workers_dev = true
# route = { pattern = "images.syndicaps.com/*", zone_name = "syndicaps.com" }

# Resource limits (disabled for free plan)
# limits = { cpu_ms = 30000 }

# KV Namespaces
kv_namespaces = [
  { binding = "IMAGE_CACHE_KV", id = "92f0140bf6fc4f37b9d464d15e96add9", preview_id = "92f0140bf6fc4f37b9d464d15e96add9" },
  { binding = "IMAGE_METADATA_KV", id = "5bc93828f9bb457f9c38996b1056524e", preview_id = "5bc93828f9bb457f9c38996b1056524e" }
]

# R2 Buckets
r2_buckets = [
  { binding = "R2_IMAGES", bucket_name = "syndicaps-images", preview_bucket_name = "syndicaps-images-preview" }
]

# Environment variables
[vars]
ENVIRONMENT = "development"

# Development environment
[env.development]
name = "syndicaps-image-optimizer-dev"
vars = { ENVIRONMENT = "development" }
kv_namespaces = [
  { binding = "IMAGE_CACHE_KV", id = "92f0140bf6fc4f37b9d464d15e96add9", preview_id = "92f0140bf6fc4f37b9d464d15e96add9" },
  { binding = "IMAGE_METADATA_KV", id = "5bc93828f9bb457f9c38996b1056524e", preview_id = "5bc93828f9bb457f9c38996b1056524e" }
]
r2_buckets = [
  { binding = "R2_IMAGES", bucket_name = "syndicaps-images-dev", preview_bucket_name = "syndicaps-images-dev-preview" }
]

# Staging environment
[env.staging]
name = "syndicaps-image-optimizer-staging"
# route = { pattern = "images-staging.syndicaps.com/*", zone_name = "syndicaps.com" }
vars = { ENVIRONMENT = "staging" }
kv_namespaces = [
  { binding = "IMAGE_CACHE_KV", id = "92f0140bf6fc4f37b9d464d15e96add9" },
  { binding = "IMAGE_METADATA_KV", id = "5bc93828f9bb457f9c38996b1056524e" }
]
r2_buckets = [
  { binding = "R2_IMAGES", bucket_name = "syndicaps-images-staging" }
]

# Production environment
[env.production]
name = "syndicaps-image-optimizer"
# route = { pattern = "images.syndicaps.com/*", zone_name = "syndicaps.com" }
vars = { ENVIRONMENT = "production" }
kv_namespaces = [
  { binding = "IMAGE_CACHE_KV", id = "92f0140bf6fc4f37b9d464d15e96add9" },
  { binding = "IMAGE_METADATA_KV", id = "5bc93828f9bb457f9c38996b1056524e" }
]
r2_buckets = [
  { binding = "R2_IMAGES", bucket_name = "syndicaps-images" }
]

# Build configuration
[build]
command = "npm run build:image-optimizer"
cwd = "."
watch_dir = "."

# Miniflare configuration for local development
[miniflare]
kv_persist = true
r2_persist = true
cache_persist = true

# Analytics and observability
[observability]
enabled = true

# Placement configuration
[placement]
mode = "smart"
