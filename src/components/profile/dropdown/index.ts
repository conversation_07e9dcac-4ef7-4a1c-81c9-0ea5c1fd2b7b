/**
 * Profile Dropdown Components Index
 * 
 * Centralized exports for all profile dropdown components.
 * Provides clean imports and better tree shaking.
 * 
 * <AUTHOR> Team
 */

// Main dropdown component
export { default as OptimizedUserProfileDropdown } from './OptimizedUserProfileDropdown'

// Core components
export { default as ProfileDropdownButton } from './ProfileDropdownButton'
export { default as ProfileDropdownHeader } from './ProfileDropdownHeader'
export { default as ProfileDropdownTabs } from './ProfileDropdownTabs'

// Section components
export { default as OverviewSection } from './sections/OverviewSection'
export { default as ActivitySection } from './sections/ActivitySection'

// Menu components
export { default as MenuSection } from './menu/MenuSection'
export { default as MenuItem } from './menu/MenuItem'
export { getMenuCategories, DEFAULT_MENU_CATEGORIES } from './menu/MenuCategories'

// Types
export type { TabId } from './ProfileDropdownTabs'
export type { MenuItem as MenuItemType, MenuCategories } from './menu/MenuCategories'
