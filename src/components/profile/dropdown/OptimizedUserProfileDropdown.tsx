/**
 * Optimized User Profile Dropdown Component
 * 
 * Refactored and optimized version of the UserProfileDropdown component.
 * Split into focused components with progressive loading and performance optimizations.
 * 
 * <AUTHOR> Team
 */

'use client'

import React, { useState, useCallback, useRef, useEffect, Suspense, lazy } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { signOut } from 'firebase/auth'
import { auth } from '@/lib/firebase'
import { useUser } from '@/lib/useUser'
import { useClickOutside } from '@/hooks/useClickOutside'
import { getMenuCategories } from './menu/MenuCategories'
import ProfileDropdownButton from './ProfileDropdownButton'
import ProfileDropdownHeader from './ProfileDropdownHeader'
import ProfileDropdownTabs, { TabId } from './ProfileDropdownTabs'
import OverviewSection from './sections/OverviewSection'

// Lazy load heavy components for better performance
const ActivitySection = lazy(() => import('./sections/ActivitySection'))

interface OptimizedUserProfileDropdownProps {
  /** Additional CSS classes */
  className?: string
}

/**
 * Content Loading Skeleton
 */
const ContentSkeleton: React.FC = () => (
  <div className="p-4 space-y-3 animate-pulse">
    <div className="h-4 bg-gray-700 rounded w-3/4" />
    <div className="h-3 bg-gray-700 rounded w-1/2" />
    <div className="h-3 bg-gray-700 rounded w-2/3" />
  </div>
)

/**
 * Optimized User Profile Dropdown Component
 */
export const OptimizedUserProfileDropdown: React.FC<OptimizedUserProfileDropdownProps> = ({
  className = ''
}) => {
  const { user, profile, loading } = useUser()
  const router = useRouter()
  
  // Component state
  const [isOpen, setIsOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<TabId>('overview')
  
  // Refs for click outside handling
  const dropdownRef = useRef<HTMLDivElement>(null)
  
  // Click outside hook
  useClickOutside(dropdownRef, () => setIsOpen(false))

  // Essential user data only

  // Generate display name
  const displayName = profile?.displayName || 
                     profile?.firstName || 
                     user?.displayName || 
                     user?.email?.split('@')[0] || 
                     'User'

  // Generate menu categories
  const menuCategories = getMenuCategories()

  /**
   * Handle dropdown toggle
   */
  const handleToggle = useCallback(() => {
    setIsOpen(prev => !prev)
  }, [])

  /**
   * Handle menu item click (closes dropdown)
   */
  const handleItemClick = useCallback(() => {
    setIsOpen(false)
  }, [])

  /**
   * Handle tab change
   */
  const handleTabChange = useCallback((tab: TabId) => {
    setActiveTab(tab)
  }, [])



  /**
   * Handle logout
   */
  const handleLogout = useCallback(async () => {
    try {
      await signOut(auth)
      router.push('/')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }, [router])

  /**
   * Handle keyboard navigation
   */
  const handleKeyDown = useCallback((event: React.KeyboardEvent) => {
    if (event.key === 'Escape') {
      setIsOpen(false)
    }
  }, [])

  // Don't render if no user
  if (!user || loading) {
    return null
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Profile Button */}
      <ProfileDropdownButton
        user={user}
        profile={profile}
        displayName={displayName}
        isOpen={isOpen}
        onClick={handleToggle}
      />

      {/* Dropdown Content */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.2, ease: 'easeOut' }}
            className="
              absolute right-0 mt-2 w-80 bg-gray-800 border border-gray-700 
              rounded-lg shadow-xl z-50 overflow-hidden
              max-h-[80vh] flex flex-col
            "
            onKeyDown={handleKeyDown}
          >
            {/* Header */}
            <ProfileDropdownHeader
              user={user}
              profile={profile}
              displayName={displayName}
            />

            {/* Tabs */}
            <ProfileDropdownTabs
              activeTab={activeTab}
              onTabChange={handleTabChange}
            />

            {/* Tab Content */}
            <div className="flex-1 overflow-y-auto">
              {activeTab === 'overview' && (
                <OverviewSection
                  menuCategories={menuCategories}
                  onItemClick={handleItemClick}
                  onLogout={handleLogout}
                />
              )}

              {activeTab === 'activity' && (
                <Suspense fallback={<ContentSkeleton />}>
                  <ActivitySection
                    userId={user.uid}
                    onItemClick={handleItemClick}
                  />
                </Suspense>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}

export default OptimizedUserProfileDropdown
