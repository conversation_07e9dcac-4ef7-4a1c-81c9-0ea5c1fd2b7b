/**
 * Profile Dropdown Header Component
 * 
 * Displays user information, tier progression, and quick stats
 * in the dropdown header section.
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { Star, Shield, Gift } from 'lucide-react'
import { User } from 'firebase/auth'
import { UserProfile } from '@/types/profile'
import RoleBadge from '@/components/ui/RoleBadge'
import { isValidRoleId, isAdminRole } from '@/constants/roles'
import { getTierInfoByPoints, getTierStyles } from '@/lib/memberTiers'

interface ProfileDropdownHeaderProps {
  /** Firebase user object */
  user: User | null
  /** User profile data */
  profile: UserProfile | null
  /** Formatted display name */
  displayName: string
}

/**
 * Profile Dropdown Header Component
 */
export const ProfileDropdownHeader: React.FC<ProfileDropdownHeaderProps> = React.memo(({
  user,
  profile,
  displayName
}) => {
  // Handle null profile gracefully - show basic user info with defaults
  if (!user) return null

  // Get tier information with safe fallbacks
  const tierInfo = getTierInfoByPoints(profile?.points || 0)
  const userTier = tierInfo.tier
  const tierName = tierInfo.name.replace(' Member', '') // Get just "Gold" instead of "Gold Member"
  const tierStyles = getTierStyles(userTier)

  // Calculate tier progress with safe fallbacks
  const currentPoints = profile?.points || 0
  const nextTierPoints = tierInfo.nextTierThreshold
  const progressPercentage = nextTierPoints
    ? Math.min((currentPoints / nextTierPoints) * 100, 100)
    : 100

  return (
    <div className="p-4 border-b border-gray-700">
      <div className="flex items-start space-x-3">
        {/* Avatar */}
        <div className="w-12 h-12 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full flex items-center justify-center flex-shrink-0">
          {profile?.avatar ? (
            <img
              src={profile.avatar}
              alt={displayName}
              className="w-full h-full rounded-full object-cover"
              loading="lazy"
            />
          ) : (
            <span className="text-white text-lg font-medium">
              {displayName.charAt(0).toUpperCase()}
            </span>
          )}
        </div>

        {/* User Info */}
        <div className="flex-1 min-w-0">
          <div>
            <h3 className="font-semibold text-white truncate">
              {displayName}
            </h3>
            <p className="text-sm text-gray-400 truncate">
              {user?.email}
            </p>
          </div>
          
          {/* Points and Tier Info */}
          <div className="flex items-center space-x-3 mt-2">
            <div className="flex items-center space-x-1">
              <Gift size={12} className="text-accent-500" />
              <span className="text-xs text-gray-400">
                {(profile?.points || 0).toLocaleString()} points
              </span>
            </div>
            {profile?.role && isValidRoleId(profile.role) && isAdminRole(profile.role) && (
              <RoleBadge
                role={profile.role}
                size="xs"
                variant="subtle"
                showTooltip={false}
              />
            )}
          </div>

          {/* Enhanced Tier Display with Progress */}
          <div className="mt-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs text-gray-400 font-medium">
                Membership Tier
              </span>
              <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-semibold ${
                userTier === 'gold'
                  ? 'bg-gradient-to-r from-yellow-500/20 to-yellow-600/20 text-yellow-400 border border-yellow-500/30'
                  : userTier === 'silver'
                  ? 'bg-gradient-to-r from-gray-400/20 to-gray-500/20 text-gray-300 border border-gray-400/30'
                  : userTier === 'platinum'
                  ? 'bg-gradient-to-r from-purple-500/20 to-purple-600/20 text-purple-400 border border-purple-500/30'
                  : 'bg-gradient-to-r from-amber-500/20 to-amber-600/20 text-amber-400 border border-amber-500/30'
              }`}>
                <Star size={10} />
                <span>{tierName}</span>
              </div>
            </div>

            {/* Tier Progress Bar */}
            {nextTierPoints && progressPercentage < 100 && (
              <div className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="text-gray-400">
                    Progress to {tierInfo.nextTier}
                  </span>
                  <span className="text-gray-400">
                    {Math.round(progressPercentage)}%
                  </span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full transition-all duration-300 ${tierStyles.progressBar}`}
                    style={{ width: `${progressPercentage}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs">
                  <span className="text-gray-500">
                    {currentPoints.toLocaleString()}
                  </span>
                  <span className="text-gray-500">
                    {nextTierPoints.toLocaleString()}
                  </span>
                </div>
              </div>
            )}

            {/* Tier Benefits Preview */}
            {tierInfo.benefits && tierInfo.benefits.length > 0 && (
              <div className="mt-2">
                <div className="text-xs text-gray-400 mb-1">Current Benefits:</div>
                <div className="flex flex-wrap gap-1">
                  {tierInfo.benefits.slice(0, 2).map((benefit, index) => (
                    <span 
                      key={index}
                      className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded"
                    >
                      {benefit}
                    </span>
                  ))}
                  {tierInfo.benefits.length > 2 && (
                    <span className="text-xs text-gray-400">
                      +{tierInfo.benefits.length - 2} more
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}, (prevProps, nextProps) => {
  // Custom comparison for optimal re-rendering
  return (
    prevProps.user?.uid === nextProps.user?.uid &&
    prevProps.profile?.points === nextProps.profile?.points &&
    prevProps.profile?.avatar === nextProps.profile?.avatar &&
    prevProps.profile?.role === nextProps.profile?.role &&
    prevProps.displayName === nextProps.displayName
  )
})

ProfileDropdownHeader.displayName = 'ProfileDropdownHeader'

export default ProfileDropdownHeader
