/**
 * Profile Dropdown Tabs Component
 * 
 * Tab navigation for the profile dropdown with mobile optimization
 * and keyboard accessibility support.
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { User, Activity } from 'lucide-react'

export type TabId = 'overview' | 'activity'

interface Tab {
  id: TabId
  label: string
  icon: React.ComponentType<{ size?: number }>
  ariaLabel: string
}

interface ProfileDropdownTabsProps {
  /** Currently active tab */
  activeTab: TabId
  /** Tab change handler */
  onTabChange: (tab: TabId) => void
  /** Additional CSS classes */
  className?: string
}

/**
 * Tab definitions
 */
const TABS: Tab[] = [
  {
    id: 'overview',
    label: 'Overview',
    icon: User,
    ariaLabel: 'Overview - Account and menu items'
  },
  {
    id: 'activity',
    label: 'Activity',
    icon: Activity,
    ariaLabel: 'Activity - Recent actions and timeline'
  }
]

/**
 * Profile Dropdown Tabs Component
 */
export const ProfileDropdownTabs: React.FC<ProfileDropdownTabsProps> = React.memo(({
  activeTab,
  onTabChange,
  className = ''
}) => {
  /**
   * Handle tab click with keyboard support
   */
  const handleTabClick = (tabId: TabId) => {
    onTabChange(tabId)
  }

  /**
   * Handle keyboard navigation
   */
  const handleKeyDown = (event: React.KeyboardEvent, tabId: TabId) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault()
      handleTabClick(tabId)
    } else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
      event.preventDefault()
      const currentIndex = TABS.findIndex(tab => tab.id === activeTab)
      const direction = event.key === 'ArrowLeft' ? -1 : 1
      const nextIndex = (currentIndex + direction + TABS.length) % TABS.length
      handleTabClick(TABS[nextIndex].id)
    }
  }

  return (
    <div className={`border-b border-gray-700 ${className}`}>
      <div 
        className="flex"
        role="tablist"
        aria-label="Profile dropdown navigation"
      >
        {TABS.map((tab) => {
          const isActive = activeTab === tab.id
          const IconComponent = tab.icon

          return (
            <button
              key={tab.id}
              role="tab"
              aria-selected={isActive}
              aria-controls={`tabpanel-${tab.id}`}
              aria-label={tab.ariaLabel}
              tabIndex={isActive ? 0 : -1}
              onClick={() => handleTabClick(tab.id)}
              onKeyDown={(e) => handleKeyDown(e, tab.id)}
              className={`
                flex-1 flex items-center justify-center space-x-2 py-3 px-4 
                text-sm font-medium transition-colors relative
                focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-inset
                ${isActive
                  ? 'text-accent-400 border-b-2 border-accent-400 bg-accent-500/5'
                  : 'text-gray-400 hover:text-gray-300 hover:bg-gray-700/30'
                }
              `}
            >
              <IconComponent size={16} />
              <span className="hidden sm:inline">
                {tab.label}
              </span>
              
              {/* Active indicator for mobile */}
              {isActive && (
                <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-accent-400 sm:hidden" />
              )}
            </button>
          )
        })}
      </div>
    </div>
  )
}, (prevProps, nextProps) => {
  // Only re-render if activeTab changes
  return prevProps.activeTab === nextProps.activeTab
})

ProfileDropdownTabs.displayName = 'ProfileDropdownTabs'

export default ProfileDropdownTabs
