/**
 * Menu Categories Data
 * 
 * Centralized menu item definitions for the profile dropdown.
 * Organized by category with proper typing and accessibility.
 * 
 * <AUTHOR> Team
 */

import {
  User,
  Settings,
  Package,
  Trophy,
  Star,
  Gift,
  Shield
} from 'lucide-react'

export interface MenuItem {
  icon: React.ComponentType<{ size?: number; className?: string }>
  label: string
  href: string
  description: string
  badge?: number
  badgeColor?: 'blue' | 'red' | 'yellow' | 'green' | 'purple'
  badgePrefix?: string
}

export interface MenuCategories {
  account: MenuItem[]
  activity: MenuItem[]
  rewards: MenuItem[]
}

/**
 * Get menu categories for essential profile functions
 */
export const getMenuCategories = (): MenuCategories => ({
  account: [
    {
      icon: User,
      label: 'Account Details',
      href: '/profile/account',
      description: 'Personal information and settings'
    },
    {
      icon: Settings,
      label: 'Preferences',
      href: '/profile/preferences',
      description: 'Customize your experience'
    },
    {
      icon: Shield,
      label: 'Privacy & Security',
      href: '/profile/privacy',
      description: 'Privacy settings and security'
    }
  ],

  activity: [
    {
      icon: Package,
      label: 'Order History',
      href: '/profile/orders',
      description: 'View your past orders'
    }
  ],

  rewards: [
    {
      icon: Star,
      label: 'Points & Rewards',
      href: '/profile/points',
      description: 'Track your points and earnings'
    },
    {
      icon: Gift,
      label: 'Reward Shop',
      href: '/rewards',
      description: 'Redeem points for exclusive items'
    }
  ]
})

/**
 * Default menu categories (without dynamic data)
 */
export const DEFAULT_MENU_CATEGORIES: MenuCategories = getMenuCategories()

export default getMenuCategories
