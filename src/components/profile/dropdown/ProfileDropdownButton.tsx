/**
 * Profile Dropdown Button Component
 * 
 * Optimized button component for the profile dropdown trigger.
 * Handles avatar display, notification badges, and tier information.
 * 
 * <AUTHOR> Team
 */

'use client'

import React from 'react'
import { ChevronDown, Star } from 'lucide-react'
import { User } from 'firebase/auth'
import { UserProfile } from '@/types/profile'
import { getTierInfoByPoints } from '@/lib/memberTiers'

interface ProfileDropdownButtonProps {
  /** Firebase user object */
  user: User | null
  /** User profile data */
  profile: UserProfile | null
  /** Formatted display name */
  displayName: string
  /** Whether dropdown is open */
  isOpen: boolean
  /** Click handler */
  onClick: () => void
  /** Additional CSS classes */
  className?: string
}

/**
 * Profile Dropdown Button Component
 */
export const ProfileDropdownButton: React.FC<ProfileDropdownButtonProps> = React.memo(({
  user,
  profile,
  displayName,
  isOpen,
  onClick,
  className = ''
}) => {
  // Get tier information
  const tierInfo = getTierInfoByPoints(profile?.points || 0)
  const userTier = tierInfo.tier

  return (
    <button
      onClick={onClick}
      className={`relative flex items-center space-x-2 text-gray-300 hover:text-white transition-colors focus:outline-none focus:ring-2 focus:ring-accent-500 focus:ring-offset-2 focus:ring-offset-gray-900 rounded-lg p-2 ${className}`}
      aria-expanded={isOpen}
      aria-haspopup="true"
      aria-label={`Profile menu for ${displayName}`}
    >

      {/* Avatar */}
      <div className="w-8 h-8 bg-gradient-to-br from-accent-500 to-accent-600 rounded-full flex items-center justify-center">
        {profile?.avatar ? (
          <img
            src={profile.avatar}
            alt={displayName}
            className="w-full h-full rounded-full object-cover"
            loading="lazy"
          />
        ) : (
          <span className="text-white text-sm font-medium">
            {displayName.charAt(0).toUpperCase()}
          </span>
        )}
      </div>

      {/* Name and Tier - Hidden on mobile */}
      <div className="hidden md:flex flex-col items-start">
        <span className="text-sm font-medium text-white truncate max-w-24">
          {displayName}
        </span>
        <div className="flex items-center space-x-1">
          <Star 
            size={12} 
            className={`${
              userTier === 'gold' ? 'text-yellow-400' :
              userTier === 'silver' ? 'text-gray-400' :
              userTier === 'platinum' ? 'text-purple-400' :
              'text-amber-600'
            }`} 
          />
          <span className="text-xs text-gray-400 capitalize">
            {userTier}
          </span>
        </div>
      </div>

      {/* Chevron indicator */}
      <ChevronDown 
        size={16} 
        className={`text-gray-400 transition-transform duration-200 ${
          isOpen ? 'rotate-180' : ''
        }`} 
      />
    </button>
  )
}, (prevProps, nextProps) => {
  // Custom comparison for optimal re-rendering
  return (
    prevProps.user?.uid === nextProps.user?.uid &&
    prevProps.profile?.points === nextProps.profile?.points &&
    prevProps.profile?.avatar === nextProps.profile?.avatar &&
    prevProps.displayName === nextProps.displayName &&
    prevProps.isOpen === nextProps.isOpen
  )
})

ProfileDropdownButton.displayName = 'ProfileDropdownButton'

export default ProfileDropdownButton
