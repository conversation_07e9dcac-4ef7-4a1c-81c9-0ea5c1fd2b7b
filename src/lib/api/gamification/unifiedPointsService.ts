/**
 * Unified Points Service
 * 
 * Single source of truth for all point balance calculations and operations.
 * Replaces multiple inconsistent point calculation methods with one secure service.
 * 
 * <AUTHOR> Team - Phase 1 Security Improvements
 * @version 2.0.0
 */

import {
  doc,
  getDoc,
  collection,
  query,
  where,
  orderBy,
  limit,
  getDocs,
  startAfter,
  Timestamp
} from 'firebase/firestore'
import { db } from '../../firebase'
import { collections } from '../../firebase/gamificationCollections'
import { SecurePointSystem } from './secureTransactions'

// ===== TYPES =====

export interface PointBalance {
  current: number
  totalEarned: number
  totalSpent: number
  lastUpdated: Date
  isVerified: boolean
}

export interface PointTransaction {
  id: string
  userId: string
  type: 'points_earned' | 'points_spent'
  amount: number
  balanceBefore: number
  balanceAfter: number
  source: string
  description: string
  timestamp: Date
  metadata: Record<string, any>
  auditTrail?: any[]
}

export interface PointHistory {
  transactions: PointTransaction[]
  totalCount: number
  hasMore: boolean
  nextCursor?: string
}

export interface PointStatistics {
  currentBalance: number
  totalEarned: number
  totalSpent: number
  transactionCount: number
  averageTransaction: number
  lastActivity: Date | null
  streakData: {
    currentStreak: number
    longestStreak: number
    lastActivityDate: Date | null
  }
}

// ===== CACHING =====

class PointBalanceCache {
  private static cache = new Map<string, { balance: PointBalance; expires: number }>()
  private static readonly CACHE_DURATION = 5 * 60 * 1000 // 5 minutes

  static get(userId: string): PointBalance | null {
    const cached = this.cache.get(userId)
    if (cached && cached.expires > Date.now()) {
      return cached.balance
    }
    return null
  }

  static set(userId: string, balance: PointBalance): void {
    this.cache.set(userId, {
      balance,
      expires: Date.now() + this.CACHE_DURATION
    })
  }

  static invalidate(userId: string): void {
    this.cache.delete(userId)
  }

  static clear(): void {
    this.cache.clear()
  }
}

// ===== UNIFIED POINTS SERVICE =====

export class UnifiedPointsService {
  /**
   * Get user's current point balance - PRIMARY METHOD
   * This is the single source of truth for point balances
   */
  static async getBalance(userId: string, useCache: boolean = true): Promise<PointBalance> {
    try {
      if (!userId) {
        throw new Error('User ID is required')
      }

      // Check cache first
      if (useCache) {
        const cached = PointBalanceCache.get(userId)
        if (cached) {
          return cached
        }
      }

      // Get user document directly for most current balance
      const userRef = doc(db, collections.users, userId)
      const userDoc = await getDoc(userRef)

      if (!userDoc.exists()) {
        throw new Error('User not found')
      }

      const userData = userDoc.data()
      const gamificationData = userData.gamification || {}

      const balance: PointBalance = {
        current: gamificationData.points || 0,
        totalEarned: gamificationData.totalPointsEarned || 0,
        totalSpent: gamificationData.totalPointsSpent || 0,
        lastUpdated: gamificationData.lastUpdate?.toDate() || new Date(),
        isVerified: false // Will be verified below
      }

      // Verify balance integrity (optional background check)
      this.verifyBalanceIntegrity(userId, balance).then(verified => {
        balance.isVerified = verified
        if (useCache) {
          PointBalanceCache.set(userId, balance)
        }
      }).catch(error => {
        console.warn('Balance verification failed:', error)
      })

      return balance
    } catch (error) {
      console.error('Error getting point balance:', error)
      throw error
    }
  }

  /**
   * Get user's point transaction history with pagination
   */
  static async getHistory(
    userId: string,
    limitCount: number = 20,
    cursor?: string
  ): Promise<PointHistory> {
    try {
      if (!userId) {
        throw new Error('User ID is required')
      }

      let q = query(
        collection(db, collections.pointTransactions),
        where('userId', '==', userId),
        orderBy('createdAt', 'desc'),
        limit(limitCount + 1) // Get one extra to check if there are more
      )

      // Apply cursor for pagination
      if (cursor) {
        const cursorDoc = await getDoc(doc(db, collections.pointTransactions, cursor))
        if (cursorDoc.exists()) {
          q = query(q, startAfter(cursorDoc))
        }
      }

      const snapshot = await getDocs(q)
      const hasMore = snapshot.docs.length > limitCount
      const transactions = snapshot.docs
        .slice(0, limitCount)
        .map(doc => this.transformTransaction(doc))

      // Get total count (for display purposes)
      const countQuery = query(
        collection(db, collections.pointTransactions),
        where('userId', '==', userId)
      )
      const countSnapshot = await getDocs(countQuery)
      const totalCount = countSnapshot.size

      return {
        transactions,
        totalCount,
        hasMore,
        nextCursor: hasMore ? transactions[transactions.length - 1]?.id : undefined
      }
    } catch (error) {
      console.error('Error getting point history:', error)
      throw error
    }
  }

  /**
   * Get comprehensive point statistics for a user
   */
  static async getStatistics(userId: string): Promise<PointStatistics> {
    try {
      const balance = await this.getBalance(userId)
      const history = await this.getHistory(userId, 100) // Get more for better statistics

      const transactions = history.transactions
      const averageTransaction = transactions.length > 0
        ? transactions.reduce((sum, t) => sum + Math.abs(t.amount), 0) / transactions.length
        : 0

      const lastActivity = transactions.length > 0 ? transactions[0].timestamp : null
      const streakData = await this.calculateStreak(userId, transactions)

      return {
        currentBalance: balance.current,
        totalEarned: balance.totalEarned,
        totalSpent: balance.totalSpent,
        transactionCount: history.totalCount,
        averageTransaction,
        lastActivity,
        streakData
      }
    } catch (error) {
      console.error('Error getting point statistics:', error)
      throw error
    }
  }

  /**
   * Award points using the secure system
   */
  static async awardPoints(
    userId: string,
    amount: number,
    source: string,
    description: string,
    metadata?: Record<string, any>,
    adminId?: string
  ): Promise<{ success: boolean; newBalance: number; transactionId?: string; error?: string }> {
    try {
      const result = await SecurePointSystem.awardPoints(
        userId,
        amount,
        source,
        description,
        metadata || {},
        adminId
      )

      if (result.success) {
        // Invalidate cache
        PointBalanceCache.invalidate(userId)
      }

      return {
        success: result.success,
        newBalance: result.newBalance || 0,
        transactionId: result.transactionId,
        error: result.error
      }
    } catch (error) {
      return {
        success: false,
        newBalance: 0,
        error: error instanceof Error ? error.message : 'Failed to award points'
      }
    }
  }

  /**
   * Spend points using the secure system
   */
  static async spendPoints(
    userId: string,
    amount: number,
    source: string,
    description: string,
    metadata?: Record<string, any>,
    adminId?: string
  ): Promise<{ success: boolean; newBalance: number; transactionId?: string; error?: string }> {
    try {
      const result = await SecurePointSystem.spendPoints(
        userId,
        amount,
        source,
        description,
        metadata || {},
        adminId
      )

      if (result.success) {
        // Invalidate cache
        PointBalanceCache.invalidate(userId)
      }

      return {
        success: result.success,
        newBalance: result.newBalance || 0,
        transactionId: result.transactionId,
        error: result.error
      }
    } catch (error) {
      return {
        success: false,
        newBalance: 0,
        error: error instanceof Error ? error.message : 'Failed to spend points'
      }
    }
  }

  /**
   * Check if user has sufficient points for a purchase
   */
  static async hasSufficientPoints(userId: string, amount: number): Promise<boolean> {
    try {
      const balance = await this.getBalance(userId)
      return balance.current >= amount
    } catch (error) {
      console.error('Error checking sufficient points:', error)
      return false
    }
  }

  /**
   * Get point balance for multiple users (for leaderboards)
   */
  static async getBulkBalances(userIds: string[]): Promise<Record<string, PointBalance>> {
    const balances: Record<string, PointBalance> = {}
    
    try {
      // Process in batches to avoid overwhelming Firebase
      const batchSize = 10
      const batches = []
      
      for (let i = 0; i < userIds.length; i += batchSize) {
        const batch = userIds.slice(i, i + batchSize)
        batches.push(batch)
      }

      for (const batch of batches) {
        const batchPromises = batch.map(userId => 
          this.getBalance(userId).catch(error => {
            console.warn(`Failed to get balance for user ${userId}:`, error)
            return {
              current: 0,
              totalEarned: 0,
              totalSpent: 0,
              lastUpdated: new Date(),
              isVerified: false
            }
          })
        )

        const batchResults = await Promise.all(batchPromises)
        batch.forEach((userId, index) => {
          balances[userId] = batchResults[index]
        })
      }
    } catch (error) {
      console.error('Error getting bulk balances:', error)
    }

    return balances
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Transform Firestore document to PointTransaction
   */
  private static transformTransaction(doc: any): PointTransaction {
    const data = doc.data()
    return {
      id: doc.id,
      userId: data.userId,
      type: data.type,
      amount: data.amount,
      balanceBefore: data.balanceBefore || 0,
      balanceAfter: data.balanceAfter || 0,
      source: data.source,
      description: data.description,
      timestamp: data.timestamp?.toDate() || new Date(),
      metadata: data.metadata || {},
      auditTrail: data.auditTrail || []
    }
  }

  /**
   * Verify balance integrity against transaction history
   */
  private static async verifyBalanceIntegrity(
    userId: string, 
    currentBalance: PointBalance
  ): Promise<boolean> {
    try {
      // This is an expensive operation, so we do it in background
      const history = await this.getHistory(userId, 1000) // Check last 1000 transactions
      
      let calculatedBalance = 0
      let totalEarned = 0
      let totalSpent = 0

      for (const transaction of history.transactions.reverse()) { // Start from oldest
        if (transaction.type === 'points_earned') {
          calculatedBalance += transaction.amount
          totalEarned += transaction.amount
        } else if (transaction.type === 'points_spent') {
          calculatedBalance += transaction.amount // amount is negative for spending
          totalSpent += Math.abs(transaction.amount)
        }
      }

      // Allow for small discrepancies due to concurrent operations
      const balanceMatches = Math.abs(calculatedBalance - currentBalance.current) <= 1
      const earnedMatches = Math.abs(totalEarned - currentBalance.totalEarned) <= 1
      const spentMatches = Math.abs(totalSpent - currentBalance.totalSpent) <= 1

      return balanceMatches && earnedMatches && spentMatches
    } catch (error) {
      console.warn('Balance verification failed:', error)
      return false
    }
  }

  /**
   * Calculate user's activity streak
   */
  private static async calculateStreak(
    userId: string,
    transactions: PointTransaction[]
  ): Promise<{ currentStreak: number; longestStreak: number; lastActivityDate: Date | null }> {
    try {
      if (transactions.length === 0) {
        return { currentStreak: 0, longestStreak: 0, lastActivityDate: null }
      }

      // Group transactions by date
      const activityDates = new Set<string>()
      transactions.forEach(transaction => {
        const dateStr = transaction.timestamp.toISOString().split('T')[0]
        activityDates.add(dateStr)
      })

      const sortedDates = Array.from(activityDates).sort().reverse()
      const today = new Date().toISOString().split('T')[0]
      
      let currentStreak = 0
      let longestStreak = 0
      let tempStreak = 0

      // Calculate current streak
      for (let i = 0; i < sortedDates.length; i++) {
        const date = new Date(sortedDates[i])
        const daysDiff = Math.floor((new Date(today).getTime() - date.getTime()) / (1000 * 60 * 60 * 24))
        
        if (i === 0 && daysDiff <= 1) {
          currentStreak = 1
          tempStreak = 1
        } else if (daysDiff === i + 1) {
          currentStreak++
          tempStreak++
        } else {
          break
        }
      }

      // Calculate longest streak
      tempStreak = 0
      let lastDate: Date | null = null
      
      for (const dateStr of sortedDates) {
        const date = new Date(dateStr)
        
        if (!lastDate || Math.floor((lastDate.getTime() - date.getTime()) / (1000 * 60 * 60 * 24)) === 1) {
          tempStreak++
          longestStreak = Math.max(longestStreak, tempStreak)
        } else {
          tempStreak = 1
        }
        
        lastDate = date
      }

      return {
        currentStreak,
        longestStreak,
        lastActivityDate: transactions[0]?.timestamp || null
      }
    } catch (error) {
      console.warn('Streak calculation failed:', error)
      return { currentStreak: 0, longestStreak: 0, lastActivityDate: null }
    }
  }

  // ===== CACHE MANAGEMENT =====

  /**
   * Clear cache for a specific user
   */
  static clearUserCache(userId: string): void {
    PointBalanceCache.invalidate(userId)
  }

  /**
   * Clear all cached balances
   */
  static clearAllCache(): void {
    PointBalanceCache.clear()
  }

  /**
   * Warm up cache for active users
   */
  static async warmUpCache(userIds: string[]): Promise<void> {
    try {
      const promises = userIds.map(userId => 
        this.getBalance(userId, false).catch(error => {
          console.warn(`Failed to warm cache for user ${userId}:`, error)
        })
      )
      
      await Promise.all(promises)
    } catch (error) {
      console.error('Cache warm-up failed:', error)
    }
  }
}

export default UnifiedPointsService