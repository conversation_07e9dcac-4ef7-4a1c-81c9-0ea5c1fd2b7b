#!/usr/bin/env npx tsx

/**
 * Cloudflare Health Check Script
 * Comprehensive validation of Cloudflare account integration and service status
 * for the Syndicaps hybrid deployment project
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'warning' | 'critical' | 'unknown';
  message: string;
  details?: any;
}

interface CloudflareConfig {
  accountId: string;
  authenticatedAccountId: string;
  email: string;
  hasValidAuth: boolean;
  tokenPermissions: string[];
}

class CloudflareHealthChecker {
  private results: HealthCheckResult[] = [];
  private config: CloudflareConfig | null = null;

  async runHealthCheck(): Promise<void> {
    console.log('🔍 Starting Cloudflare Health Check for Syndicaps...\n');

    // 1. Authentication Status
    await this.checkAuthentication();

    // 2. Configuration Validation
    await this.checkConfiguration();

    // 3. Service Connectivity
    await this.checkServiceConnectivity();

    // 4. Resource Health
    await this.checkResourceHealth();

    // 5. Performance Status
    await this.checkPerformanceStatus();

    // Generate Report
    this.generateReport();
  }

  private async checkAuthentication(): Promise<void> {
    console.log('🔐 Checking Authentication Status...');

    try {
      const whoamiOutput = execSync('npx wrangler whoami', { 
        encoding: 'utf-8',
        cwd: path.join(process.cwd(), 'workers')
      });

      // Parse authentication info
      const emailMatch = whoamiOutput.match(/associated with the email (.+?)\./);
      const accountIdMatch = whoamiOutput.match(/│\s+(.+?)\s+│\s+([a-f0-9]{32})\s+│/);
      const permissionsMatch = whoamiOutput.match(/Scope \(Access\)([\s\S]*?)(?=\n\n|\n$)/);

      if (emailMatch && accountIdMatch) {
        this.config = {
          accountId: this.getConfiguredAccountId(),
          authenticatedAccountId: accountIdMatch[2],
          email: emailMatch[1],
          hasValidAuth: true,
          tokenPermissions: this.parsePermissions(permissionsMatch?.[1] || '')
        };

        // Check if account IDs match
        if (this.config.accountId !== this.config.authenticatedAccountId) {
          this.results.push({
            service: 'Authentication',
            status: 'warning',
            message: 'Account ID mismatch detected',
            details: {
              configured: this.config.accountId,
              authenticated: this.config.authenticatedAccountId,
              email: this.config.email
            }
          });
        } else {
          this.results.push({
            service: 'Authentication',
            status: 'healthy',
            message: 'Successfully authenticated with matching account ID',
            details: {
              email: this.config.email,
              accountId: this.config.authenticatedAccountId,
              permissions: this.config.tokenPermissions.length
            }
          });
        }
      } else {
        throw new Error('Failed to parse authentication information');
      }
    } catch (error) {
      this.results.push({
        service: 'Authentication',
        status: 'critical',
        message: `Authentication failed: ${error}`,
        details: { error: String(error) }
      });
    }
  }

  private async checkConfiguration(): Promise<void> {
    console.log('⚙️  Checking Configuration Files...');

    // Check wrangler.toml
    const wranglerTomlPath = path.join(process.cwd(), 'wrangler.toml');
    const workersWranglerTomlPath = path.join(process.cwd(), 'workers', 'wrangler.toml');

    try {
      if (fs.existsSync(wranglerTomlPath)) {
        const content = fs.readFileSync(wranglerTomlPath, 'utf-8');
        const hasAccountId = content.includes('account_id');
        const hasKvNamespaces = content.includes('kv_namespaces');
        const hasR2Buckets = content.includes('r2_buckets');

        this.results.push({
          service: 'Configuration - Root wrangler.toml',
          status: hasAccountId && hasKvNamespaces && hasR2Buckets ? 'healthy' : 'warning',
          message: 'Configuration file found and parsed',
          details: {
            hasAccountId,
            hasKvNamespaces,
            hasR2Buckets,
            path: wranglerTomlPath
          }
        });
      }

      if (fs.existsSync(workersWranglerTomlPath)) {
        const content = fs.readFileSync(workersWranglerTomlPath, 'utf-8');
        this.results.push({
          service: 'Configuration - Workers wrangler.toml',
          status: 'healthy',
          message: 'Workers configuration file found',
          details: { path: workersWranglerTomlPath }
        });
      }

      // Check environment variables
      const envPath = path.join(process.cwd(), '.env.local');
      if (fs.existsSync(envPath)) {
        const envContent = fs.readFileSync(envPath, 'utf-8');
        const hasCloudflareConfig = envContent.includes('CLOUDFLARE_ACCOUNT_ID');
        const hasR2Config = envContent.includes('R2_ACCESS_KEY_ID');

        this.results.push({
          service: 'Configuration - Environment Variables',
          status: hasCloudflareConfig ? 'healthy' : 'warning',
          message: 'Environment configuration checked',
          details: {
            hasCloudflareConfig,
            hasR2Config,
            path: envPath
          }
        });
      }
    } catch (error) {
      this.results.push({
        service: 'Configuration',
        status: 'critical',
        message: `Configuration check failed: ${error}`,
        details: { error: String(error) }
      });
    }
  }

  private async checkServiceConnectivity(): Promise<void> {
    console.log('🌐 Checking Service Connectivity...');

    // Test R2 Storage
    await this.testR2Connectivity();

    // Test KV Storage
    await this.testKVConnectivity();

    // Test Workers
    await this.testWorkersConnectivity();

    // Test Pages
    await this.testPagesConnectivity();
  }

  private async testR2Connectivity(): Promise<void> {
    try {
      const output = execSync('npx wrangler r2 bucket list', {
        encoding: 'utf-8',
        cwd: path.join(process.cwd(), 'workers'),
        timeout: 15000
      });

      if (output.includes('Authentication error')) {
        this.results.push({
          service: 'R2 Storage',
          status: 'critical',
          message: 'Authentication error accessing R2 buckets',
          details: { error: 'Account ID mismatch or insufficient permissions' }
        });
      } else {
        // Parse bucket list
        const buckets = this.parseR2Buckets(output);
        this.results.push({
          service: 'R2 Storage',
          status: buckets.length > 0 ? 'healthy' : 'warning',
          message: `Found ${buckets.length} R2 buckets`,
          details: { buckets }
        });
      }
    } catch (error) {
      this.results.push({
        service: 'R2 Storage',
        status: 'critical',
        message: `R2 connectivity failed: ${error}`,
        details: { error: String(error) }
      });
    }
  }

  private async testKVConnectivity(): Promise<void> {
    try {
      const output = execSync('npx wrangler kv namespace list', {
        encoding: 'utf-8',
        cwd: path.join(process.cwd(), 'workers'),
        timeout: 15000
      });

      if (output.includes('Authentication error')) {
        this.results.push({
          service: 'KV Storage',
          status: 'critical',
          message: 'Authentication error accessing KV namespaces',
          details: { error: 'Account ID mismatch or insufficient permissions' }
        });
      } else {
        const namespaces = this.parseKVNamespaces(output);
        this.results.push({
          service: 'KV Storage',
          status: namespaces.length > 0 ? 'healthy' : 'warning',
          message: `Found ${namespaces.length} KV namespaces`,
          details: { namespaces }
        });
      }
    } catch (error) {
      this.results.push({
        service: 'KV Storage',
        status: 'critical',
        message: `KV connectivity failed: ${error}`,
        details: { error: String(error) }
      });
    }
  }

  private async testWorkersConnectivity(): Promise<void> {
    try {
      const output = execSync('npx wrangler deployments list', {
        encoding: 'utf-8',
        cwd: path.join(process.cwd(), 'workers'),
        timeout: 15000
      });

      if (output.includes('Authentication error')) {
        this.results.push({
          service: 'Workers',
          status: 'critical',
          message: 'Authentication error accessing Workers',
          details: { error: 'Account ID mismatch or insufficient permissions' }
        });
      } else {
        const deployments = this.parseWorkerDeployments(output);
        this.results.push({
          service: 'Workers',
          status: deployments.length > 0 ? 'healthy' : 'warning',
          message: `Found ${deployments.length} worker deployments`,
          details: { deployments }
        });
      }
    } catch (error) {
      this.results.push({
        service: 'Workers',
        status: 'warning',
        message: `Workers connectivity check failed: ${error}`,
        details: { error: String(error) }
      });
    }
  }

  private async testPagesConnectivity(): Promise<void> {
    try {
      const output = execSync('npx wrangler pages project list', {
        encoding: 'utf-8',
        cwd: path.join(process.cwd(), 'workers'),
        timeout: 15000
      });

      const projects = this.parsePagesProjects(output);
      this.results.push({
        service: 'Pages',
        status: projects.length > 0 ? 'healthy' : 'warning',
        message: `Found ${projects.length} Pages projects`,
        details: { projects }
      });
    } catch (error) {
      this.results.push({
        service: 'Pages',
        status: 'warning',
        message: `Pages connectivity check failed: ${error}`,
        details: { error: String(error) }
      });
    }
  }

  private async checkResourceHealth(): Promise<void> {
    console.log('🏥 Checking Resource Health...');

    // Check if expected resources exist based on our 12-week project
    const expectedResources = {
      r2Buckets: ['syndicaps-images', 'syndicaps-backups'],
      kvNamespaces: ['CACHE_KV', 'SESSION_KV'],
      workers: ['syndicaps-image-optimizer', 'syndicaps-api-cache']
    };

    // This would be implemented based on the actual resource checks above
    this.results.push({
      service: 'Resource Health',
      status: 'unknown',
      message: 'Resource health check requires valid authentication',
      details: { expectedResources }
    });
  }

  private async checkPerformanceStatus(): Promise<void> {
    console.log('📊 Checking Performance Status...');

    // Check if performance monitoring is active
    const monitoringFiles = [
      'docs/week-12-final-project-review-and-handoff.md',
      'scripts/performance-validation.ts',
      'components/admin/CloudflareHybridDashboard.tsx'
    ];

    const existingFiles = monitoringFiles.filter(file => 
      fs.existsSync(path.join(process.cwd(), file))
    );

    this.results.push({
      service: 'Performance Monitoring',
      status: existingFiles.length === monitoringFiles.length ? 'healthy' : 'warning',
      message: `Performance monitoring components: ${existingFiles.length}/${monitoringFiles.length} found`,
      details: { existingFiles, missingFiles: monitoringFiles.filter(f => !existingFiles.includes(f)) }
    });
  }

  // Helper methods
  private getConfiguredAccountId(): string {
    try {
      const wranglerToml = fs.readFileSync(path.join(process.cwd(), 'wrangler.toml'), 'utf-8');
      const match = wranglerToml.match(/account_id\s*=\s*"([^"]+)"/);
      return match?.[1] || 'unknown';
    } catch {
      return 'unknown';
    }
  }

  private parsePermissions(permissionsText: string): string[] {
    return permissionsText
      .split('\n')
      .map(line => line.trim())
      .filter(line => line.startsWith('- '))
      .map(line => line.substring(2));
  }

  private parseR2Buckets(output: string): string[] {
    // Parse R2 bucket list output
    const lines = output.split('\n');
    return lines
      .filter(line => line.trim() && !line.includes('Listing buckets') && !line.includes('wrangler'))
      .map(line => line.trim());
  }

  private parseKVNamespaces(output: string): string[] {
    // Parse KV namespace list output
    const lines = output.split('\n');
    return lines
      .filter(line => line.trim() && !line.includes('Listing namespaces') && !line.includes('wrangler'))
      .map(line => line.trim());
  }

  private parseWorkerDeployments(output: string): string[] {
    // Parse worker deployments output
    const lines = output.split('\n');
    return lines
      .filter(line => line.trim() && !line.includes('Listing deployments') && !line.includes('wrangler'))
      .map(line => line.trim());
  }

  private parsePagesProjects(output: string): string[] {
    // Parse Pages projects output
    const lines = output.split('\n');
    return lines
      .filter(line => line.trim() && !line.includes('Listing projects') && !line.includes('wrangler'))
      .map(line => line.trim());
  }

  private generateReport(): void {
    console.log('\n📋 CLOUDFLARE HEALTH CHECK REPORT');
    console.log('=====================================\n');

    const healthyCount = this.results.filter(r => r.status === 'healthy').length;
    const warningCount = this.results.filter(r => r.status === 'warning').length;
    const criticalCount = this.results.filter(r => r.status === 'critical').length;
    const unknownCount = this.results.filter(r => r.status === 'unknown').length;

    console.log(`📊 Summary: ${healthyCount} Healthy | ${warningCount} Warnings | ${criticalCount} Critical | ${unknownCount} Unknown\n`);

    // Group results by status
    const statusOrder = ['critical', 'warning', 'unknown', 'healthy'];
    const statusEmojis = {
      healthy: '✅',
      warning: '⚠️',
      critical: '🚨',
      unknown: '❓'
    };

    statusOrder.forEach(status => {
      const statusResults = this.results.filter(r => r.status === status);
      if (statusResults.length > 0) {
        console.log(`${statusEmojis[status as keyof typeof statusEmojis]} ${status.toUpperCase()} ISSUES:`);
        statusResults.forEach(result => {
          console.log(`  • ${result.service}: ${result.message}`);
          if (result.details && Object.keys(result.details).length > 0) {
            console.log(`    Details: ${JSON.stringify(result.details, null, 2).replace(/\n/g, '\n    ')}`);
          }
        });
        console.log('');
      }
    });

    // Recommendations
    this.generateRecommendations();

    // Save report to file
    const reportData = {
      timestamp: new Date().toISOString(),
      summary: { healthyCount, warningCount, criticalCount, unknownCount },
      results: this.results,
      config: this.config
    };

    const reportPath = path.join(process.cwd(), 'reports', `cloudflare-health-check-${new Date().toISOString().split('T')[0]}.json`);
    fs.mkdirSync(path.dirname(reportPath), { recursive: true });
    fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
    console.log(`📄 Full report saved to: ${reportPath}`);
  }

  private generateRecommendations(): void {
    console.log('💡 RECOMMENDATIONS:');
    console.log('===================\n');

    const criticalIssues = this.results.filter(r => r.status === 'critical');
    const warningIssues = this.results.filter(r => r.status === 'warning');

    if (criticalIssues.length > 0) {
      console.log('🚨 CRITICAL - Immediate Action Required:');
      
      const authIssues = criticalIssues.filter(r => r.service.includes('Authentication'));
      if (authIssues.length > 0) {
        console.log('  1. Fix Account ID Mismatch:');
        console.log('     - Update wrangler.toml account_id to match authenticated account');
        console.log(`     - Change account_id from "${this.config?.accountId}" to "${this.config?.authenticatedAccountId}"`);
        console.log('     - Re-run setup scripts with correct account ID');
      }

      const serviceIssues = criticalIssues.filter(r => r.service.includes('R2') || r.service.includes('KV') || r.service.includes('Workers'));
      if (serviceIssues.length > 0) {
        console.log('  2. Recreate Cloudflare Resources:');
        console.log('     - Run: npm run setup:cloudflare-infrastructure');
        console.log('     - Verify R2 buckets and KV namespaces are created');
        console.log('     - Update environment variables with new resource IDs');
      }
    }

    if (warningIssues.length > 0) {
      console.log('\n⚠️  WARNINGS - Recommended Actions:');
      
      const configIssues = warningIssues.filter(r => r.service.includes('Configuration'));
      if (configIssues.length > 0) {
        console.log('  1. Complete Configuration Setup:');
        console.log('     - Fill in missing environment variables in .env.local');
        console.log('     - Verify all Cloudflare API tokens and secrets');
      }

      const resourceIssues = warningIssues.filter(r => r.service.includes('Resource'));
      if (resourceIssues.length > 0) {
        console.log('  2. Deploy Missing Resources:');
        console.log('     - Deploy workers: npm run deploy:workers');
        console.log('     - Create missing R2 buckets and KV namespaces');
      }
    }

    console.log('\n📚 Next Steps:');
    console.log('  1. Fix critical issues first');
    console.log('  2. Re-run this health check: npm run health-check:cloudflare');
    console.log('  3. Deploy and test hybrid features once all services are healthy');
    console.log('  4. Monitor performance using the Cloudflare dashboard\n');
  }
}

// Run the health check
async function main() {
  const checker = new CloudflareHealthChecker();
  await checker.runHealthCheck();
}

if (require.main === module) {
  main().catch(console.error);
}

export { CloudflareHealthChecker };
