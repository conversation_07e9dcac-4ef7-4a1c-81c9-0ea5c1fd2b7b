{"timestamp": "2025-07-27T08:03:39.577Z", "summary": {"healthyCount": 9, "warningCount": 0, "criticalCount": 0, "unknownCount": 1}, "results": [{"service": "Authentication", "status": "healthy", "message": "Successfully authenticated with matching account ID", "details": {"email": "syndicaps22@gmail", "accountId": "24a0c424061d2684ac21f5dd6284f906", "permissions": 18}}, {"service": "Configuration - Root wrangler.toml", "status": "healthy", "message": "Configuration file found and parsed", "details": {"hasAccountId": true, "hasKvNamespaces": true, "hasR2Buckets": true, "path": "/Users/<USER>/Developer/syndicaps/wrangler.toml"}}, {"service": "Configuration - Workers wrangler.toml", "status": "healthy", "message": "Workers configuration file found", "details": {"path": "/Users/<USER>/Developer/syndicaps/workers/wrangler.toml"}}, {"service": "Configuration - Environment Variables", "status": "healthy", "message": "Environment configuration checked", "details": {"hasCloudflareConfig": true, "hasR2Config": true, "path": "/Users/<USER>/Developer/syndicaps/.env.local"}}, {"service": "R2 Storage", "status": "healthy", "message": "Found 13 R2 buckets", "details": {"buckets": ["───────────────────", "name:           syndicaps-backups", "creation_date:  2025-07-26T16:58:48.146Z", "name:           syndicaps-backups-dev", "creation_date:  2025-07-26T16:58:10.524Z", "name:           syndicaps-backups-staging", "creation_date:  2025-07-26T16:58:29.231Z", "name:           syndicaps-images", "creation_date:  2025-07-26T16:58:39.200Z", "name:           syndicaps-images-dev", "creation_date:  2025-07-26T16:57:59.723Z", "name:           syndicaps-images-staging", "creation_date:  2025-07-26T16:58:19.495Z"]}}, {"service": "KV Storage", "status": "healthy", "message": "Found 74 KV namespaces", "details": {"namespaces": ["[", "{", "\"id\": \"62c46dc1f510470b910ad2687eeafff9\",", "\"title\": \"API_CACHE_KV\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"3b900ea35e88423e9fdae8e948eb375c\",", "\"title\": \"API_METADATA_KV\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"71cabead02ef4f448b6b9ebf392fee0c\",", "\"title\": \"CACHE_KV_DEV\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"a0cc4dfdf2ac4655be3369661d79455b\",", "\"title\": \"CACHE_KV_PROD\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"17ddf6927df24c45a47cab108095526a\",", "\"title\": \"CACHE_KV_STAGING\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"92f0140bf6fc4f37b9d464d15e96add9\",", "\"title\": \"IMAGE_CACHE_KV\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"5bc93828f9bb457f9c38996b1056524e\",", "\"title\": \"IMAGE_METADATA_KV\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"9c755f8b0a624122b1d4ed0dd944c07d\",", "\"title\": \"OAUTH_KV\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"9ce53fdd5d4b41b185e64389af9e56e7\",", "\"title\": \"RATE_LIMIT_KV\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"800ef077f93240b791cd22e4e7d222d2\",", "\"title\": \"SESSION_KV_DEV\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"c25f365c2b06419ea1cf1f4cdafe02e9\",", "\"title\": \"SESSION_KV_PROD\",", "\"supports_url_encoding\": true,", "\"beta\": false", "},", "{", "\"id\": \"8ed4d59d0fbf4a31a5b9f4e78dd1a75f\",", "\"title\": \"SESSION_KV_STAGING\",", "\"supports_url_encoding\": true,", "\"beta\": false", "}", "]"]}}, {"service": "Workers", "status": "healthy", "message": "Found 16 worker deployments", "details": {"deployments": ["Created:     2025-07-27T03:15:55.299Z", "Author:      <EMAIL>", "Source:      Upload", "Message:     Automatic deployment on upload.", "Version(s):  (100%) 4fb4ad5f-39fc-48c1-ac43-e3bdf2778ec8", "Created:  2025-07-27T03:15:55.299Z", "Tag:  -", "Message:  -", "Created:     2025-07-27T03:17:20.187Z", "Author:      <EMAIL>", "Source:      Unknown (deployment)", "Message:     -", "Version(s):  (100%) ad0a4881-a531-416e-be60-4684dc84e2f4", "Created:  2025-07-27T03:17:17.278Z", "Tag:  -", "Message:  -"]}}, {"service": "Pages", "status": "healthy", "message": "Found 1 Pages projects", "details": {"projects": ["───────────────────"]}}, {"service": "Resource Health", "status": "unknown", "message": "Resource health check requires valid authentication", "details": {"expectedResources": {"r2Buckets": ["syndicaps-images", "syndicaps-backups"], "kvNamespaces": ["CACHE_KV", "SESSION_KV"], "workers": ["syndicaps-image-optimizer", "syndicaps-api-cache"]}}}, {"service": "Performance Monitoring", "status": "healthy", "message": "Performance monitoring components: 3/3 found", "details": {"existingFiles": ["docs/week-12-final-project-review-and-handoff.md", "scripts/performance-validation.ts", "components/admin/CloudflareHybridDashboard.tsx"], "missingFiles": []}}], "config": {"accountId": "24a0c424061d2684ac21f5dd6284f906", "authenticatedAccountId": "24a0c424061d2684ac21f5dd6284f906", "email": "syndicaps22@gmail", "hasValidAuth": true, "tokenPermissions": ["account (read)", "user (read)", "workers (write)", "workers_kv (write)", "workers_routes (write)", "workers_scripts (write)", "workers_tail (read)", "d1 (write)", "pages (write)", "zone (read)", "ssl_certs (write)", "ai (write)", "queues (write)", "pipelines (write)", "secrets_store (write)", "containers (write)", "cloudchamber (write)", "offline_access"]}}